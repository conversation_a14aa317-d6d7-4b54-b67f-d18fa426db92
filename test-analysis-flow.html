<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品分析流程测试</title>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: var(--background-color);
            font-family: var(--font-family);
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            text-align: center;
            color: var(--text-primary);
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }
        
        .chat-container {
            margin-top: 30px;
            min-height: 400px;
            border: 1px solid var(--border-light);
            border-radius: 8px;
            padding: 20px;
            background: var(--gray-50);
        }
        
        .button-group {
            text-align: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">商品分析流程测试</h1>
        
        <div class="button-group">
            <button class="test-button" onclick="testNormalFlow()">
                <i class="ri-shopping-bag-line"></i> 测试普通商品分析流程
            </button>
            <button class="test-button" onclick="testIndependentFlow()">
                <i class="ri-store-line"></i> 测试独立站商品分析流程
            </button>
            <button class="test-button" onclick="clearChat()">
                <i class="ri-delete-bin-line"></i> 清空聊天记录
            </button>
        </div>
        
        <div class="chat-container" id="chat-container">
            <p style="text-align: center; color: var(--text-secondary); margin-top: 100px;">
                点击上方按钮开始测试分析流程
            </p>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // 测试普通商品分析流程
        function testNormalFlow() {
            clearChat();
            
            // 添加用户消息
            addUserMessage('https://www.amazon.com/dp/B08N5WRWNW');
            
            // 开始分析流程
            analyzeProductLink('https://www.amazon.com/dp/B08N5WRWNW');
        }
        
        // 测试独立站商品分析流程
        function testIndependentFlow() {
            clearChat();
            
            // 添加用户消息
            addUserMessage('https://example-store.com/products/smart-earbuds-pro');
            
            // 开始独立站分析流程
            simulateIndependentStoreAnalysis('https://example-store.com/products/smart-earbuds-pro');
        }
        
        // 清空聊天记录
        function clearChat() {
            const chatContainer = document.getElementById('chat-container');
            chatContainer.innerHTML = '<p style="text-align: center; color: var(--text-secondary); margin-top: 100px;">点击上方按钮开始测试分析流程</p>';
        }
        
        // 重写addUserMessage和addAIMessage函数以适配测试页面
        function addUserMessage(message) {
            const chatContainer = document.getElementById('chat-container');
            if (chatContainer.innerHTML.includes('点击上方按钮')) {
                chatContainer.innerHTML = '';
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user-message';
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-text">${message}</div>
                </div>
            `;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        function addAIMessage(content) {
            const chatContainer = document.getElementById('chat-container');
            
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ai-message';
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-text">${content}</div>
                </div>
            `;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
    </script>
</body>
</html>
