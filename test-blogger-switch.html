<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博主切换功能测试</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
</head>
<body>
    <div style="padding: 20px; max-width: 900px; margin: 0 auto;">
        <h1>博主切换功能测试</h1>
        
        <!-- 模拟邮件生成结果卡片 -->
        <div class="email-generation-container">
            <div class="email-generation-header">
                <h4><i class="ri-mail-line"></i> 建联邮件生成结果</h4>
            </div>
            <div class="email-generation-content">
                <div class="selected-creators-section">
                    <div class="section-title">选中的博主 (3):</div>
                    <div class="selected-creators-list">
                        <div class="selected-creator-item">MattVidPro AI</div>
                        <div class="selected-creator-item">Two Minute Papers</div>
                        <div class="selected-creator-item">Sciencephile the AI</div>
                    </div>
                </div>

                <div class="creator-switch-section">
                    <div class="section-title">查看不同博主的邮件:</div>
                    <div class="creator-switch-container">
                        <div class="creator-switch-option active" data-creator="MattVidPro AI">
                            MattVidPro AI
                        </div>
                        <div class="creator-switch-option" data-creator="Two Minute Papers">
                            Two Minute Papers
                        </div>
                        <div class="creator-switch-option" data-creator="Sciencephile the AI">
                            Sciencephile the AI
                        </div>
                    </div>
                </div>

                <div class="email-preview-section">
                    <div class="section-title">邮件预览:</div>
                    <div class="email-preview-container">
                        <div class="email-preview-header">
                            <div class="email-field">
                                <div class="email-field-label">收件人:</div>
                                <div class="email-field-value current-recipient">MattVidPro AI</div>
                            </div>
                            <div class="email-field">
                                <div class="email-field-label">主题:</div>
                                <div class="email-field-value">
                                    <input type="text" class="email-subject-input" value="Earbud 智能翻译耳机合作邀请 - AI技术创新产品">
                                </div>
                            </div>
                        </div>
                        <div class="email-preview-body">
                            <textarea class="email-body-input" rows="15">尊敬的 MattVidPro AI 博主：

您好！我是 Earbud 智能翻译耳机的产品运营经理。作为AI技术领域的专业创作者，您在视频制作和AI应用方面的深度见解让我印象深刻，特别是您对AI语音识别技术的专业分析。

我们的 Earbud 智能翻译耳机正是AI技术在音频设备上的突破性应用，采用先进的神经网络翻译引擎，支持40+种语言的实时翻译，并配备智能降噪和高保真音质技术。这款产品完美契合您频道对前沿AI技术的关注点。

我们希望与您合作，展示这款AI驱动的创新产品：

1. AI翻译技术深度解析和实测
2. 与传统翻译设备的技术对比
3. 在不同场景下的AI性能表现
4. 产品的AI算法优化特性展示

合作条件：
- 免费提供 Earbud 智能翻译耳机产品（市场价值 $99）
- 视频发布后的合作费用：$1,500-$2,500
- 为您的粉丝提供专属20%折扣码
- 优先体验我们后续的AI产品

期待与您这样的AI技术专家合作，共同探索AI在音频领域的无限可能！

如有兴趣，请回复此邮件或联系我：123-4567-8910

祝好，
[您的名字]
Earbud 产品运营经理</textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="email-generation-actions">
                <button class="regenerate-email-btn"><i class="ri-refresh-line"></i> 重新生成</button>
                <button class="send-email-btn primary-btn"><i class="ri-send-plane-fill"></i> 发送邮件</button>
            </div>
        </div>
    </div>

    <script>
        // 博主邮件模板数据
        const creatorEmailTemplates = {
            'MattVidPro AI': {
                subject: 'Earbud 智能翻译耳机合作邀请 - AI技术创新产品',
                content: `尊敬的 MattVidPro AI 博主：

您好！我是 Earbud 智能翻译耳机的产品运营经理。作为AI技术领域的专业创作者，您在视频制作和AI应用方面的深度见解让我印象深刻，特别是您对AI语音识别技术的专业分析。

我们的 Earbud 智能翻译耳机正是AI技术在音频设备上的突破性应用，采用先进的神经网络翻译引擎，支持40+种语言的实时翻译，并配备智能降噪和高保真音质技术。这款产品完美契合您频道对前沿AI技术的关注点。

我们希望与您合作，展示这款AI驱动的创新产品：

1. AI翻译技术深度解析和实测
2. 与传统翻译设备的技术对比
3. 在不同场景下的AI性能表现
4. 产品的AI算法优化特性展示

合作条件：
- 免费提供 Earbud 智能翻译耳机产品（市场价值 $99）
- 视频发布后的合作费用：$1,500-$2,500
- 为您的粉丝提供专属20%折扣码
- 优先体验我们后续的AI产品

期待与您这样的AI技术专家合作，共同探索AI在音频领域的无限可能！

如有兴趣，请回复此邮件或联系我：123-4567-8910

祝好，
[您的名字]
Earbud 产品运营经理`
            },
            'Two Minute Papers': {
                subject: 'Earbud AI翻译耳机 - 前沿语音AI技术研究合作',
                content: `尊敬的 Two Minute Papers 团队：

您好！我是 Earbud 智能翻译耳机的产品运营经理。您的频道在AI研究领域的权威性和对前沿技术的深度解析令人敬佩，特别是在语音AI和机器学习方面的专业内容。

我们的 Earbud 智能翻译耳机基于最新的Transformer架构和端到端神经机器翻译技术，实现了低延迟、高精度的实时语音翻译。这项技术突破正是您频道经常探讨的AI研究成果的实际应用。

我们希望邀请您从学术角度分析这款产品：

1. 神经机器翻译技术的实际应用效果
2. 实时语音处理的算法优化分析
3. 多语言模型的性能基准测试
4. 与当前学术研究成果的对比分析

合作方案：
- 免费提供产品及技术文档供研究分析
- 合作费用：$3,000-$5,000
- 提供技术团队深度访谈机会
- 独家技术细节披露权限

我们相信这次合作能为您的观众带来AI技术从研究到产品化的完整视角。

期待您的回复！

最诚挚的问候，
[您的名字]
Earbud 产品运营经理`
            },
            'Sciencephile the AI': {
                subject: 'AI意识与语言：Earbud智能翻译耳机的哲学思考',
                content: `亲爱的 Sciencephile the AI：

您好！我是 Earbud 智能翻译耳机的产品运营经理。您独特的AI视角和对人工智能哲学思考的深度内容让我深受启发，特别是您对AI语言理解和意识的探讨。

我们的 Earbud 智能翻译耳机不仅是一款技术产品，更是AI理解和处理人类语言的具体体现。它能实时翻译40+种语言，这背后涉及深层的语言学习、语义理解和跨文化交流的AI挑战。

从您的独特角度，这款产品可以引发很多有趣的讨论：

1. AI如何真正"理解"不同语言的含义？
2. 机器翻译是否能传达语言背后的文化内涵？
3. 实时翻译技术对人类交流方式的影响
4. AI语言处理的局限性和未来发展

合作提议：
- 免费提供产品进行深度体验和分析
- 合作费用：$2,000-$3,500
- 支持您从AI哲学角度的独特解读
- 提供技术背景资料供深度思考

让我们一起探索AI语言技术的深层意义！

期待您的哲学性分析，
[您的名字]
Earbud 产品运营经理`
            }
        };

        // 博主切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const creatorSwitchOptions = document.querySelectorAll('.creator-switch-option');
            const emailSubjectInput = document.querySelector('.email-subject-input');
            const emailBodyInput = document.querySelector('.email-body-input');
            const currentRecipient = document.querySelector('.current-recipient');

            creatorSwitchOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // 移除所有active状态
                    creatorSwitchOptions.forEach(opt => opt.classList.remove('active'));
                    // 添加当前选中的active状态
                    this.classList.add('active');

                    // 获取选中的博主名称
                    const selectedCreator = this.dataset.creator;
                    
                    // 更新收件人显示
                    if (currentRecipient) {
                        currentRecipient.textContent = selectedCreator;
                    }

                    // 获取对应的邮件模板
                    const template = creatorEmailTemplates[selectedCreator];
                    if (template) {
                        // 更新邮件主题和内容
                        if (emailSubjectInput) {
                            emailSubjectInput.value = template.subject;
                        }
                        if (emailBodyInput) {
                            emailBodyInput.value = template.content;
                        }

                        // 添加切换动画效果
                        emailBodyInput.style.opacity = '0.5';
                        setTimeout(() => {
                            emailBodyInput.style.opacity = '1';
                        }, 200);
                    }
                });
            });
        });
    </script>
</body>
</html>
